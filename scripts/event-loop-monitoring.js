#!/usr/bin/env node

/**
 * Event Loop Monitoring Script
 * 
 * This script can be used to monitor event loop delays in production
 * Run with: node scripts/event-loop-monitoring.js
 */

const { performance } = require('perf_hooks');

class EventLoopMonitor {
  constructor(options = {}) {
    this.interval = options.interval || 5000; // Check every 5 seconds
    this.warningThreshold = options.warningThreshold || 100; // Warn if delay > 100ms
    this.criticalThreshold = options.criticalThreshold || 500; // Critical if delay > 500ms
    this.isRunning = false;
    this.intervalId = null;
    this.measurements = [];
    this.maxMeasurements = options.maxMeasurements || 100;
  }

  measureEventLoopDelay() {
    return new Promise((resolve) => {
      const start = performance.now();
      setImmediate(() => {
        const delay = performance.now() - start;
        resolve(delay);
      });
    });
  }

  async logMeasurement() {
    const delay = await this.measureEventLoopDelay();
    const timestamp = new Date().toISOString();
    
    // Store measurement
    this.measurements.push({ timestamp, delay });
    
    // Keep only recent measurements
    if (this.measurements.length > this.maxMeasurements) {
      this.measurements.shift();
    }

    // Log based on severity
    if (delay > this.criticalThreshold) {
      console.error(`🚨 CRITICAL: Event loop delay ${delay.toFixed(2)}ms at ${timestamp}`);
    } else if (delay > this.warningThreshold) {
      console.warn(`⚠️  WARNING: Event loop delay ${delay.toFixed(2)}ms at ${timestamp}`);
    } else {
      console.log(`✅ OK: Event loop delay ${delay.toFixed(2)}ms at ${timestamp}`);
    }

    return delay;
  }

  getStats() {
    if (this.measurements.length === 0) {
      return null;
    }

    const delays = this.measurements.map(m => m.delay);
    const avg = delays.reduce((sum, d) => sum + d, 0) / delays.length;
    const min = Math.min(...delays);
    const max = Math.max(...delays);
    const p95 = this.percentile(delays, 95);
    const p99 = this.percentile(delays, 99);

    return {
      count: delays.length,
      average: avg.toFixed(2),
      min: min.toFixed(2),
      max: max.toFixed(2),
      p95: p95.toFixed(2),
      p99: p99.toFixed(2),
      timeRange: {
        start: this.measurements[0].timestamp,
        end: this.measurements[this.measurements.length - 1].timestamp
      }
    };
  }

  percentile(arr, p) {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index];
  }

  start() {
    if (this.isRunning) {
      console.log('Event loop monitor is already running');
      return;
    }

    console.log(`🔍 Starting event loop monitoring (interval: ${this.interval}ms)`);
    console.log(`⚠️  Warning threshold: ${this.warningThreshold}ms`);
    console.log(`🚨 Critical threshold: ${this.criticalThreshold}ms`);
    
    this.isRunning = true;
    
    // Initial measurement
    this.logMeasurement();
    
    // Set up periodic monitoring
    this.intervalId = setInterval(() => {
      this.logMeasurement();
    }, this.interval);

    // Log stats every minute
    setInterval(() => {
      const stats = this.getStats();
      if (stats) {
        console.log('\n📊 Event Loop Stats (last', stats.count, 'measurements):');
        console.log(`   Average: ${stats.average}ms`);
        console.log(`   Min/Max: ${stats.min}ms / ${stats.max}ms`);
        console.log(`   95th percentile: ${stats.p95}ms`);
        console.log(`   99th percentile: ${stats.p99}ms\n`);
      }
    }, 60000);
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping event loop monitoring');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    // Final stats
    const stats = this.getStats();
    if (stats) {
      console.log('\n📊 Final Event Loop Stats:');
      console.log(JSON.stringify(stats, null, 2));
    }
  }
}

// CLI usage
if (require.main === module) {
  const monitor = new EventLoopMonitor({
    interval: process.env.MONITOR_INTERVAL || 5000,
    warningThreshold: process.env.WARNING_THRESHOLD || 100,
    criticalThreshold: process.env.CRITICAL_THRESHOLD || 500
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    monitor.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    monitor.stop();
    process.exit(0);
  });

  monitor.start();
}

module.exports = EventLoopMonitor;
