-- Script to check for orphaned activity log entries
-- This identifies activity log entries that reference deleted entities

-- Check for activity logs with company_id that don't exist in companies table
SELECT 
    'orphaned_company_references' as issue_type,
    COUNT(*) as count,
    array_agg(DISTINCT al.company_id) as orphaned_company_ids,
    array_agg(DISTINCT al.event_type) as event_types
FROM activity_log al
LEFT JOIN companies c ON al.company_id = c.id
WHERE al.company_id IS NOT NULL 
  AND c.id IS NULL;

-- Check for activity logs with benefit_id that don't exist in benefits table  
SELECT 
    'orphaned_benefit_references' as issue_type,
    COUNT(*) as count,
    array_agg(DISTINCT al.benefit_id) as orphaned_benefit_ids,
    array_agg(DISTINCT al.event_type) as event_types
FROM activity_log al
LEFT JOIN benefits b ON al.benefit_id = b.id
WHERE al.benefit_id IS NOT NULL 
  AND b.id IS NULL;

-- Check for activity logs with user_id that don't exist in users table
-- Note: This is expected for user deletion events, but unexpected for other events
SELECT 
    'orphaned_user_references' as issue_type,
    COUNT(*) as count,
    array_agg(DISTINCT al.user_id) as orphaned_user_ids,
    array_agg(DISTINCT al.event_type) as event_types
FROM activity_log al
LEFT JOIN users u ON al.user_id = u.id
WHERE al.user_id IS NOT NULL 
  AND u.id IS NULL
  AND al.event_type NOT IN ('user_deleted'); -- Exclude expected user deletion events

-- Detailed view of orphaned company references
SELECT 
    al.id,
    al.event_type,
    al.event_description,
    al.company_id,
    al.company_name,
    al.created_at,
    al.metadata
FROM activity_log al
LEFT JOIN companies c ON al.company_id = c.id
WHERE al.company_id IS NOT NULL 
  AND c.id IS NULL
ORDER BY al.created_at DESC;

-- Detailed view of orphaned benefit references
SELECT 
    al.id,
    al.event_type,
    al.event_description,
    al.benefit_id,
    al.benefit_name,
    al.created_at,
    al.metadata
FROM activity_log al
LEFT JOIN benefits b ON al.benefit_id = b.id
WHERE al.benefit_id IS NOT NULL 
  AND b.id IS NULL
ORDER BY al.created_at DESC;

-- Check for failed activity log insertions in recent logs
-- This would show up as constraint violations in application logs
SELECT 
    event_type,
    COUNT(*) as failed_attempts,
    MAX(created_at) as last_failure
FROM activity_log 
WHERE event_description LIKE '%constraint%' 
   OR event_description LIKE '%foreign key%'
   OR event_description LIKE '%violation%'
GROUP BY event_type
ORDER BY failed_attempts DESC;
